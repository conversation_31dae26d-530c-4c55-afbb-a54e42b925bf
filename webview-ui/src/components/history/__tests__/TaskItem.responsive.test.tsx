import { render, screen } from "@testing-library/react"
import TaskItem from "../TaskItem"
import type { HistoryItem } from "@roo-code/types"

jest.mock("@src/utils/vscode")
jest.mock("@src/i18n/TranslationContext", () => ({
	useAppTranslation: () => ({
		t: (key: string) => key,
	}),
}))

const createMockItem = (task: string, workspace?: string): HistoryItem => ({
	id: "test-id",
	number: 1,
	task,
	ts: Date.now(),
	tokensIn: 100,
	tokensOut: 50,
	totalCost: 0.002,
	workspace,
	taskType: "local" as const,
})

describe("TaskItem Responsive Layout", () => {
	it("should handle very long file paths without breaking layout", () => {
		const longFilePath = "Please analyze the file /very/long/path/to/some/deeply/nested/directory/structure/with/many/subdirectories/and/a/very/long/filename/that/could/potentially/break/the/layout/if/not/handled/properly/example.tsx and provide suggestions for improvement."
		
		const mockItem = createMockItem(longFilePath)

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		// Verify the task content is rendered
		expect(screen.getByTestId("task-content")).toBeInTheDocument()
		
		// Verify the copy button is rendered and accessible
		expect(screen.getByTestId("copy-prompt-button")).toBeInTheDocument()
		
		// Verify the task content has proper responsive classes
		const taskContent = screen.getByTestId("task-content")
		expect(taskContent).toHaveClass("flex-1")
		expect(taskContent).toHaveClass("min-w-0")
		expect(taskContent).toHaveClass("break-words")
		expect(taskContent).toHaveClass("text-ellipsis")
		expect(taskContent).toHaveClass("whitespace-pre-wrap")
		expect(taskContent).toHaveClass("text-vscode-foreground")
		
		// Verify the layout uses flex items-start gap-2
		const contentContainer = taskContent.parentElement
		expect(contentContainer).toHaveClass("flex")
		expect(contentContainer).toHaveClass("items-start")
		expect(contentContainer).toHaveClass("gap-2")
	})

	it("should handle very long workspace paths without breaking layout", () => {
		const longWorkspacePath = "/Users/<USER>/very/long/workspace/path/with/many/nested/directories/and/subdirectories/that/could/potentially/cause/layout/issues/my-project"
		const mockItem = createMockItem("Test task with normal length content", longWorkspacePath)

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={true}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		// Verify workspace info is displayed
		const workspaceContainer = screen.getByText(longWorkspacePath).parentElement
		expect(workspaceContainer).toBeInTheDocument()
		
		// Verify workspace container has proper responsive classes
		expect(workspaceContainer).toHaveClass("min-w-0")
		
		// Verify workspace path text has break-all class
		const workspaceText = screen.getByText(longWorkspacePath)
		expect(workspaceText).toHaveClass("break-all")
		expect(workspaceText).toHaveClass("overflow-hidden")
		expect(workspaceText).toHaveClass("text-ellipsis")
		
		// Verify folder icon has shrink-0 class
		const folderIcon = workspaceContainer?.querySelector('.codicon-folder')
		expect(folderIcon).toHaveClass("shrink-0")
	})

	it("should ensure main container has proper flex classes", () => {
		const mockItem = createMockItem("Test task content")

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		// Find the main content container (the flex-1 div)
		const taskContent = screen.getByTestId("task-content")
		const mainContainer = taskContent.closest('.flex-1')
		
		expect(mainContainer).toBeInTheDocument()
		expect(mainContainer).toHaveClass("flex-1")
		expect(mainContainer).toHaveClass("min-w-0")
	})

	it("should not show copy button in selection mode", () => {
		const mockItem = createMockItem("Test task content")

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={true}
				isSelected={false}
			/>
		)

		// Copy button should not be rendered in selection mode
		expect(screen.queryByTestId("copy-prompt-button")).not.toBeInTheDocument()
	})

	it("should handle compact variant properly with long paths", () => {
		const longFilePath = "Please analyze this very long file path that should be handled properly in compact mode /very/long/path/example.tsx"
		const mockItem = createMockItem(longFilePath)

		render(
			<TaskItem
				item={mockItem}
				variant="compact"
				showWorkspace={false}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		// Verify task content has line-clamp-2 for compact variant
		const taskContent = screen.getByTestId("task-content")
		expect(taskContent).toHaveClass("line-clamp-2")
		expect(taskContent).toHaveClass("break-words")
		expect(taskContent).toHaveClass("min-w-0")
	})

	it("should handle remote task type with long content", () => {
		const longTask = "This is a very long remote task description that should wrap properly and not break the layout when displayed in the task item component"
		const mockItem: HistoryItem = {
			...createMockItem(longTask),
			taskType: "remote",
			remoteTaskId: "remote-123",
			roomoteApiUrl: "https://api.example.com",
		}

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		// Verify remote badge is displayed
		expect(screen.getByText("Remote")).toBeInTheDocument()
		
		// Verify remote badge container has shrink-0 class
		const remoteBadge = screen.getByText("Remote").closest('.shrink-0')
		expect(remoteBadge).toBeInTheDocument()
		
		// Verify task content still has proper responsive classes
		const taskContent = screen.getByTestId("task-content")
		expect(taskContent).toHaveClass("flex-1")
		expect(taskContent).toHaveClass("min-w-0")
		expect(taskContent).toHaveClass("break-words")
	})

	it("should handle selection checkbox without breaking layout", () => {
		const longTask = "This is a very long task that should not break the layout even when selection checkbox is present"
		const mockItem = createMockItem(longTask)

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={true}
				isSelected={false}
				onToggleSelection={jest.fn()}
			/>
		)

		// Verify checkbox is present
		expect(screen.getByRole("checkbox")).toBeInTheDocument()
		
		// Verify checkbox container has shrink-0 class
		const checkboxContainer = screen.getByRole("checkbox").closest('.shrink-0')
		expect(checkboxContainer).toBeInTheDocument()
		
		// Verify main content still has proper flex classes
		const taskContent = screen.getByTestId("task-content")
		const mainContainer = taskContent.closest('.flex-1')
		expect(mainContainer).toHaveClass("min-w-0")
	})
})
