import { render, screen } from "@testing-library/react"
import type { HistoryItem } from "@roo-code/types"
import TaskItem from "../TaskItem"

// Mock the vscode module
jest.mock("@/utils/vscode", () => ({
	vscode: {
		postMessage: jest.fn(),
	},
}))

const createMockItem = (task: string): HistoryItem => ({
	id: "test-id",
	ts: Date.now(),
	task,
	tokensIn: 100,
	tokensOut: 50,
	totalCost: 0.01,
	clineMessages: [],
})

describe("TaskItem Responsive Layout", () => {
	it("should handle very long file paths without breaking layout", () => {
		const longFilePath = "Please analyze the file /very/long/path/to/some/deeply/nested/directory/structure/with/many/subdirectories/and/a/very/long/filename/that/could/potentially/break/the/layout/if/not/handled/properly/example.tsx and provide suggestions for improvement."
		
		const mockItem = createMockItem(longFilePath)

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		// Verify the task content is rendered
		expect(screen.getByTestId("task-content")).toBeInTheDocument()
		
		// Verify the copy button is rendered and accessible
		expect(screen.getByTestId("copy-prompt-button")).toBeInTheDocument()
		
		// Verify the task content has proper responsive classes
		const taskContent = screen.getByTestId("task-content")
		expect(taskContent).toHaveClass("flex-1")
		expect(taskContent).toHaveClass("min-w-0")
		expect(taskContent).toHaveClass("break-words")
		expect(taskContent).toHaveClass("break-anywhere")
		expect(taskContent).toHaveClass("overflow-hidden")
		expect(taskContent).toHaveClass("text-ellipsis")
		
		// Verify the layout uses flex items-start gap-2
		const contentContainer = taskContent.parentElement
		expect(contentContainer).toHaveClass("flex")
		expect(contentContainer).toHaveClass("items-start")
		expect(contentContainer).toHaveClass("gap-2")
	})

	it("should ensure copy button container has flex-shrink-0 class", () => {
		const mockItem = createMockItem("Test task with normal length content")

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		// Find the copy button's parent container
		const copyButton = screen.getByTestId("copy-prompt-button")
		const buttonContainer = copyButton.closest(".flex-shrink-0")
		
		expect(buttonContainer).toBeInTheDocument()
	})

	it("should not show copy button in selection mode", () => {
		const mockItem = createMockItem("Test task content")

		render(
			<TaskItem
				item={mockItem}
				variant="full"
				showWorkspace={false}
				isSelectionMode={true}
				isSelected={false}
			/>
		)

		// Copy button should not be rendered in selection mode
		expect(screen.queryByTestId("copy-prompt-button")).not.toBeInTheDocument()
	})

	it("should handle compact variant properly", () => {
		const longFilePath = "Please analyze this very long file path that should be handled properly in compact mode /very/long/path/example.tsx"
		const mockItem = createMockItem(longFilePath)

		render(
			<TaskItem
				item={mockItem}
				variant="compact"
				showWorkspace={false}
				isSelectionMode={false}
				isSelected={false}
			/>
		)

		const taskContent = screen.getByTestId("task-content")
		
		// Should have line-clamp-2 for compact variant
		expect(taskContent).toHaveClass("line-clamp-2")
		expect(taskContent).not.toHaveClass("line-clamp-3")
		
		// Should still have responsive classes
		expect(taskContent).toHaveClass("flex-1")
		expect(taskContent).toHaveClass("min-w-0")
		expect(taskContent).toHaveClass("break-words")
		expect(taskContent).toHaveClass("break-anywhere")
	})
})
