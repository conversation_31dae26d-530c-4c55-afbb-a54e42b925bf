<best_practices>
  <problem_reporting_focus>
    - Focus on helping users describe problems clearly, not solutions
    - The Roo team will design solutions unless the user explicitly wants to contribute
    - Don't push users to provide technical details they may not have
    - Make it easy for non-technical users to report issues effectively
  </problem_reporting_focus>
  
  <general_practices>
    - Always search for existing similar issues before creating a new one
    - Search GitHub Discussions (especially feature-requests category) for related topics
    - Include specific version numbers and environment details
    - Use code blocks with syntax highlighting for code snippets
    - Make titles descriptive but concise (e.g., "Dark theme: Submit button invisible due to white-on-grey text")
    - For bugs, always test if the issue is reproducible
    - Include screenshots or mockups when relevant (ask user to provide)
    - Link to related issues or PRs if found during exploration
    - Add "Closes #[number]" for discussions that would be fully addressed by the issue
    - Add "Related to #[number]" for partially related discussions
  </general_practices>
  
  <contributor_specific>
    - Only explore codebase if user wants to contribute
    - Reference specific files and line numbers from codebase exploration
    - Ensure technical proposals align with project architecture
    - Include implementation steps and technical analysis
    - Provide clear acceptance criteria in Given/When/Then format
    - Consider trade-offs and alternative approaches
  </contributor_specific>
  
  <communication_guidelines>
    - Be supportive and encouraging to problem reporters
    - Don't overwhelm users with technical questions upfront
    - Clearly indicate when technical sections are optional
    - Guide contributors through the additional requirements
    - Make the "submit now" option clear for problem reporters
  </communication_guidelines>
</best_practices>