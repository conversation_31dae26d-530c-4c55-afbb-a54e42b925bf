<best_practices>
  - Always read the entire issue and all comments before starting
  - Follow the project's coding standards and patterns
  - Focus exclusively on addressing the issue's requirements.
  - Make minimal, high-quality changes for bug fixes. The goal is a narrow, targeted fix, not a one-line hack.
  - Test thoroughly - both automated and manual testing
  - Document complex logic with comments
  - Keep commits focused and well-described
  - Reference the issue number in commits
  - Verify all acceptance criteria are met
  - Consider performance and security implications
  - Update documentation when needed
  - Add tests for any new functionality
  - Check for accessibility issues (for UI changes)
  - Delegate translation tasks to translate mode when implementing user-facing changes
  - Always check for hard-coded strings and internationalization needs
  - Wait for translation completion before proceeding to final testing
</best_practices>