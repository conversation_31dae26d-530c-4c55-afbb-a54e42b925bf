<analysis_techniques>
  <overview>
    Techniques for analyzing code to extract documentation.
  </overview>

  <code_analysis_techniques>
    <technique name="entry_point_analysis">
      <description>
        Analyze entry points to understand feature flow.
      </description>
      <steps>
        <step>Find main functions, controllers, or route handlers.</step>
        <step>Trace execution flow.</step>
        <step>Map decision branches.</step>
        <step>Document input validation.</step>
      </steps>
      <tools><![CDATA[
<!-- Find entry points -->
<codebase_search>
<query>main function app.listen server.start router controller handler</query>
</codebase_search>

<!-- Analyze specific entry point -->
<read_file>
<path>src/controllers/feature.controller.ts</path>
</read_file>

<!-- Find all routes -->
<search_files>
<path>src</path>
<regex>(app\.(get|post|put|delete)|@(Get|Post|Put|Delete)|router\.(get|post|put|delete))</regex>
</search_files>
      ]]></tools>
    </technique>

    <technique name="api_extraction">
      <description>
        Extract API specifications from code.
      </description>
      <patterns>
        <pattern type="rest">
          <search_regex><![CDATA[
(app|router)\.(get|post|put|patch|delete)\s*\(\s*['"`]([^'"`]+)['"`]
          ]]></search_regex>
          <extraction>
            - HTTP method
            - Route path
            - Path/query parameters
            - Request/response schemas
            - Status codes
          </extraction>
        </pattern>
        <pattern type="graphql">
          <search_regex><![CDATA[
type\s+(Query|Mutation|Subscription)\s*{[^}]+}|@(Query|Mutation|Resolver)
          ]]></search_regex>
          <extraction>
            - Schema and input types
            - Resolvers
            - Return types
            - Field arguments
          </extraction>
        </pattern>
      </patterns>
    </technique>

    <technique name="dependency_mapping">
      <description>
        Map dependencies and integration points.
      </description>
      <analysis_points>
        <point>Import/require statements</point>
        <point>package.json dependencies</point>
        <point>External API calls</point>
        <point>DB connections</point>
        <point>Message queue integrations</point>
        <point>Filesystem operations</point>
      </analysis_points>
      <tools><![CDATA[
<!-- Find all imports -->
<search_files>
<path>src</path>
<regex>^import\s+.*from\s+['"]([^'"]+)['"]|require\s*\(\s*['"]([^'"]+)['"]\s*\)</regex>
</search_files>

<!-- Analyze package dependencies -->
<read_file>
<path>package.json</path>
</read_file>

<!-- Find external API calls -->
<search_files>
<path>src</path>
<regex>(fetch|axios|http\.request|request\(|\.get\(|\.post\()</regex>
</search_files>
      ]]></tools>
    </technique>

    <technique name="data_model_extraction">
      <description>
        Extract data models, schemas, and type definitions.
      </description>
      <sources>
        <source type="typescript">
          <patterns>
            - interfaces, types, classes, enums
          </patterns>
        </source>
        <source type="database">
          <patterns>
            - Schema definitions, migration files, ORM models
          </patterns>
        </source>
        <source type="validation">
          <patterns>
            - JSON Schema, Joi/Yup/Zod schemas, validation decorators
          </patterns>
        </source>
      </sources>
      <extraction_example><![CDATA[
<!-- Find TypeScript interfaces -->
<search_files>
<path>src</path>
<regex>^export\s+(interface|type|class|enum)\s+(\w+)</regex>
</search_files>

<!-- Find database models -->
<search_files>
<path>src/models</path>
<regex>@(Entity|Table|Model)|class\s+\w+\s+extends\s+(Model|BaseEntity)</regex>
</search_files>
      ]]></extraction_example>
    </technique>

    <technique name="business_logic_extraction">
      <description>
        Identify and document business rules.
      </description>
      <indicators>
        <indicator>Complex conditionals</indicator>
        <indicator>Calculation functions</indicator>
        <indicator>Validation rules</indicator>
        <indicator>State machines</indicator>
        <indicator>Domain-specific constants and algorithms</indicator>
      </indicators>
      <documentation_focus>
        <focus>Why logic exists (business need)</focus>
        <focus>When logic applies (conditions)</focus>
        <focus>What logic does (transformation)</focus>
        <focus>Edge cases</focus>
        <focus>Impact of changes</focus>
      </documentation_focus>
    </technique>

    <technique name="error_handling_analysis">
      <description>
        Document error handling and recovery.
      </description>
      <analysis_areas>
        <area>try/catch blocks, error boundaries</area>
        <area>Custom error classes</area>
        <area>Error codes and messages</area>
        <area>Logging, fallbacks, retries, circuit breakers</area>
      </analysis_areas>
      <search_patterns><![CDATA[
<!-- Find error handling -->
<search_files>
<path>src</path>
<regex>try\s*{|catch\s*\(|throw\s+new|class\s+\w*Error\s+extends</regex>
</search_files>

<!-- Find error constants -->
<search_files>
<path>src</path>
<regex>ERROR_|_ERROR|ErrorCode|errorCode</regex>
</search_files>
      ]]></search_patterns>
    </technique>

    <technique name="security_analysis">
      <description>
        Identify security measures and vulnerabilities.
      </description>
      <security_checks>
        <check category="authentication">
          <patterns>
            - JWT, sessions, OAuth, API keys
          </patterns>
        </check>
        <check category="authorization">
          <patterns>
            - RBAC, permission checks, ownership validation
          </patterns>
        </check>
        <check category="data_protection">
          <patterns>
            - Encryption, hashing, sensitive data handling
          </patterns>
        </check>
        <check category="input_validation">
          <patterns>
            - Sanitization, SQLi/XSS/CSRF prevention
          </parents>
        </check>
      </security_checks>
    </technique>

    <technique name="performance_analysis">
      <description>
        Identify performance factors and optimization opportunities.
      </description>
      <analysis_points>
        <point>DB query patterns (N+1)</point>
        <point>Caching strategies</point>
        <point>Async usage</point>
        <point>Batch processing</point>
        <point>Resource pooling</point>
        <point>Memory management</point>
        <point>Algorithm complexity</point>
      </analysis_points>
      <metrics_to_document>
        <metric>Time/space complexity</metric>
        <metric>DB query counts</metric>
        <metric>API response times</metric>
        <metric>Memory usage</metric>
        <metric>Concurrency handling</metric>
      </metrics_to_document>
    </technique>

    <technique name="test_coverage_analysis">
      <description>
        Analyze test coverage.
      </description>
      <test_types>
        <type name="unit">
          <location>__tests__, *.test.ts, *.spec.ts</location>
          <analysis>Function coverage</analysis>
        </type>
        <type name="integration">
          <location>integration/, e2e/</location>
          <analysis>Workflow coverage</analysis>
        </type>
        <type name="api">
          <location>api-tests/, *.api.test.ts</location>
          <analysis>Endpoint coverage</analysis>
        </type>
      </test_types>
      <coverage_analysis><![CDATA[
<!-- Find test files -->
<search_files>
<path>src</path>
<regex>\.(test|spec)\.(ts|js|tsx|jsx)$</regex>
<file_pattern>*.test.ts</file_pattern>
</search_files>

<!-- Analyze test descriptions -->
<search_files>
<path>src</path>
<regex>(describe|it|test)\s*\(\s*['"`]([^'"`]+)['"`]</regex>
</search_files>
      ]]></coverage_analysis>
    </technique>

    <technique name="configuration_extraction">
      <description>
        Extract configuration options and their impacts.
      </description>
      <configuration_sources>
        <source>.env files, config files, CLI args, feature flags</source>
      </configuration_sources>
      <documentation_requirements>
        <requirement>Default values</requirement>
        <requirement>Valid values</requirement>
        <requirement>Behavior impact</requirement>
        <requirement>Config dependencies</requirement>
        <requirement>Security implications</requirement>
      </documentation_requirements>
    </technique>
  </code_analysis_techniques>

  <workflow_analysis>
    <technique name="user_journey_mapping">
      <description>
        Map user workflows through the feature.
      </description>
      <steps>
        <step>Identify entry points (UI, API, CLI).</step>
        <step>Trace user actions.</step>
        <step>Document decision points.</step>
        <step>Map data transformations.</step>
        <step>Identify outcomes.</step>
      </steps>
      <deliverables>
        <deliverable>Flow diagrams, procedures, decision trees, state diagrams.</deliverable>
      </deliverables>
    </technique>

    <technique name="integration_flow_analysis">
      <description>
        Document integration with other systems.
      </description>
      <integration_types>
        <type>Sync API calls, async messaging, events, batch processing, streaming.</type>
      </integration_types>
      <documentation_focus>
        <focus>Protocols, auth, error handling, data transforms, SLAs.</focus>
      </documentation_focus>
    </technique>
  </workflow_analysis>

  <metadata_extraction>
    <technique name="version_compatibility">
      <sources>
        <source>package.json, READMEs, migration guides, breaking changes docs.</source>
      </sources>
      <extraction_pattern><![CDATA[
<!-- Find version requirements -->
<search_files>
<path>.</path>
<regex>"engines":|"peerDependencies":|requires?\s+\w+\s+version|compatible\s+with</regex>
</search_files>
      ]]></extraction_pattern>
    </technique>

    <technique name="deprecation_tracking">
      <indicators>
        <indicator>@deprecated, TODO comments, legacy code markers.</indicator>
      </indicators>
      <documentation_requirements>
        <requirement>Deprecation date, removal timeline, migration path, alternatives.</requirement>
      </documentation_requirements>
    </technique>
  </metadata_extraction>

  <quality_indicators>
    <indicator name="documentation_completeness">
      <checks>
        <check>Public APIs documented.</check>
        <check>Examples for complex features.</check>
        <check>Error scenarios covered.</check>
        <check>Config options explained.</check>
        <check>Security addressed.</check>
      </checks>
    </indicator>

    <indicator name="code_quality_metrics">
      <metrics>
        <metric>Cyclomatic complexity, code duplication, test coverage, doc coverage, tech debt.</metric>
      </metrics>
    </indicator>
  </quality_indicators>
</analysis_techniques>