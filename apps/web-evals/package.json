{"name": "@roo-code/web-evals", "version": "0.0.0", "type": "module", "scripts": {"lint": "next lint --max-warnings 0", "check-types": "tsc -b", "dev": "scripts/check-services.sh && next dev", "format": "prettier --write src", "build": "next build", "start": "next start", "clean": "rimraf .next .turbo"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@roo-code/evals": "workspace:^", "@roo-code/types": "workspace:^", "@tanstack/react-query": "^5.69.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.0", "fuzzysort": "^3.1.0", "lucide-react": "^0.518.0", "next": "^15.2.5", "next-themes": "^0.4.6", "p-map": "^7.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-use": "^17.6.0", "redis": "^5.5.5", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@tailwindcss/postcss": "^4", "@types/ps-tree": "^1.1.6", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.5", "tailwindcss": "^4", "vitest": "^3.2.3"}}